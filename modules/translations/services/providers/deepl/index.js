import getAxiosInstance from '../../shared/client.js';
import {
  supportedSourceLanguages,
  supportedTargetLanguages,
} from './translationLanguages.js';

const url = 'https://api-free.deepl.com/v2/translate';
const timeout = 20000; // 20 seconds timeout for the request

export async function getDeepLTranslation({
  text,
  sourceLang,
  targetLang,
  apiKey,
}) {
  if (!apiKey) {
    return {
      error: 'DeepL API key is missing',
    };
  }

  // Format the source and target languages to match DeepL's requirements
  sourceLang = formatLocale(sourceLang);
  targetLang = formatLocale(targetLang);

  if (supportedSourceLanguages.includes(sourceLang)) {
    return {
      error: `DeepL does not support source language ${sourceLang}`,
    };
  }

  if (supportedTargetLanguages.includes(targetLang)) {
    return {
      error: `DeepL does not support target language ${targetLang}`,
    };
  }

  const params = {
    text: [text],
    source_lang: sourceLang,
    target_lang: targetLang,
  };

  const headers = {
    Authorization: `DeepL-Auth-Key ${apiKey || ''}`,
  };

  const client = getAxiosInstance({ timeout });

  try {
    const { data } = await client.post(url, params, { headers });

    const { translations } = data;

    return {
      translatedText: translations?.[0]?.text || '',
    };
  } catch (error) {
    return {
      error,
    };
  }
}

// DeepL requires uppercase language codes, with dashes instead of underscores
function formatLocale(lang) {
  return lang.toUpperCase().replace('_', '-');
}
