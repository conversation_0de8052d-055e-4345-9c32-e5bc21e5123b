import axios from 'axios';

import Logger from '#utils/logger.js';

import importCollections from './functions/collections.js';
import importAdvisors from './functions/advisors.js';
import importStudents from './functions/students.js';
import importCourses from './functions/courses.js';
import importCountries from './functions/countries.js';

import IMPORT_SETTINGS from './settings.js';

const {
  COURSES_IMPORT_API_URL,
  COURSES_IMPORT_USERNAME,
  COURSES_IMPORT_PASSWORD,
  COURSES_IMPORT_CLIENT_TOKEN,
  COURSES_IMPORT_ORIGIN,
} = process.env;

const importData = async function (sourceEntityId, entity, providerId) {
  Logger.info('Starting courses import');

  if (!sourceEntityId) {
    Logger.error('Source entity missing');
    return { status: 500, error: 'Source entity missing' };
  }
  if (!entity) {
    Logger.error('Destination entity missing');
    return { status: 404, error: 'Destination entity missing' };
  }
  if (!providerId) {
    Logger.error('Provider missing');
    return { status: 404, error: 'Provider missing' };
  }

  // Initialize axios
  const _axios = axios.create({
    baseURL: COURSES_IMPORT_API_URL,
    headers: {
      ClientToken: COURSES_IMPORT_CLIENT_TOKEN,
      Origin: COURSES_IMPORT_ORIGIN,
    },
  });

  // Login and set authorization token
  const { data } = await _axios.post('/api/accounts/login', {
    email: COURSES_IMPORT_USERNAME,
    password: COURSES_IMPORT_PASSWORD,
  });
  _axios.defaults.headers.common.Authorization = data.id;

  // Import advisors
  let advisors;
  if (IMPORT_SETTINGS.advisors.enabled) {
    advisors = await importAdvisors(
      entity,
      providerId,
      _axios,
      IMPORT_SETTINGS.advisors.testAdvisor
    );
  }

  // Import countries
  let countries;
  if (IMPORT_SETTINGS.countries.enabled) {
    countries = await importCountries(sourceEntityId, providerId, _axios);
  }

  // Import courses, lessons, slides, questionnaires
  let courses;
  if (IMPORT_SETTINGS.courses.enabled) {
    courses = await importCourses(
      sourceEntityId,
      entity,
      providerId,
      _axios,
      IMPORT_SETTINGS.courses.testCourse,
      IMPORT_SETTINGS.courses.options
    );
  }

  // Import categories (collections)
  let collections;
  if (IMPORT_SETTINGS.collections.enabled) {
    collections = await importCollections(
      sourceEntityId,
      providerId,
      _axios,
      IMPORT_SETTINGS.collections.testCollection
    );
  }

  // Import students, course statuses, questionnaire statuses, answers
  let students;
  if (IMPORT_SETTINGS.students.enabled) {
    students = await importStudents(
      entity,
      providerId,
      _axios,
      IMPORT_SETTINGS.students.testUsername,
      IMPORT_SETTINGS.students.options
    );
  }

  Logger.info('Done');

  return {
    ...(IMPORT_SETTINGS.advisors.enabled && { advisors }),
    ...(IMPORT_SETTINGS.countries.enabled && { countries }),
    ...(IMPORT_SETTINGS.courses.enabled && { courses }),
    ...(IMPORT_SETTINGS.collections.enabled && { collections }),
    ...(IMPORT_SETTINGS.students.enabled && { students }),
  };
};

export default {
  importData,
};
