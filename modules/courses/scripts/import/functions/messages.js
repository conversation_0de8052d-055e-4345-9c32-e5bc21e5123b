import Logger from '#utils/logger.js';

import Chat from '../../../models/Chat.js';
import ChatMessage from '../../../models/ChatMessage.js';

async function importMessages(student, advisorIds, providerId, axios) {
  // Delete existing chats & messages
  await deleteChats(student._id);

  // Create chat for each advisor
  for (const advisorId of advisorIds) {
    await importChat(student._id, advisorId, providerId, axios);
  }
}

async function importChat(studentId, advisorId, providerId, axios) {
  try {
    // Get all messages
    const { data: messages } = await axios.get(
      `/api/students/${studentId}/messages`,
      {
        params: {
          filter: {
            where: {
              or: [
                {
                  'senderId': advisorId,
                  'senderType': 'Account',
                  'receivers.receiverId': studentId,
                  'receivers.receiverType': 'Student',
                },
                {
                  'senderId': studentId,
                  'senderType': 'Student',
                  'receivers.receiverId': advisorId,
                  'receivers.receiverType': 'Account',
                },
              ],
            },
            order: 'createdAt ASC',
            limit: 100000,
          },
        },
      }
    );

    // if (messages.length === 0) {
    //   Logger.info('No messages found to import');
    //   return;
    // }

    // Get existing chat
    let existingChat = await Chat.findOne({
      // provider: providerId,
      type: 'basic',
      $and: [
        { 'members.type': 'CourseStudent', 'members.id': studentId },
        { 'members.type': 'CourseAdvisor', 'members.id': advisorId },
      ],
    });

    const lastMessage =
      messages.length > 0 ? messages[messages.length - 1] : undefined;

    const baseChat = {
      type: 'basic',
      provider: providerId,
      members: [
        { id: studentId, type: 'CourseStudent' },
        { id: advisorId, type: 'CourseAdvisor' },
      ],
      ...(lastMessage && {
        lastMessage: lastMessage.id,
        lastMessageDate: lastMessage.createdAt,
      }),
    };

    // Update existing chat
    if (existingChat) {
      Logger.info(`    - Updating existing chat`);
      await existingChat.updateOne(baseChat);
    }

    // Create new chat
    else {
      Logger.info(`    - Creating new chat`);
      existingChat = await Chat.create(baseChat);
    }

    for (const message of messages) {
      await importMessage(
        message,
        existingChat._id,
        advisorId,
        studentId,
        providerId
      );
    }
  } catch (error) {
    Logger.error(`Error when trying to load messages`, error);
  }
}

async function deleteChats(studentId) {
  // Get student chats
  const studentChats = await Chat.find(
    {
      'type': 'basic',
      'members.type': 'CourseStudent',
      'members.id': studentId,
    },
    { _id: 1 }
  );
  const chatIds = studentChats.map((chat) => chat._id);

  // Delete all student messages
  await ChatMessage.deleteMany({
    chat: { $in: chatIds },
  });

  // Delete all student chats
  await Chat.deleteMany({ _id: { $in: chatIds } });
}

async function importMessage(
  message,
  chatId,
  advisorId,
  studentId,
  providerId
) {
  try {
    let existingMessage = await ChatMessage.findOne({
      _id: message.id,
    });

    // console.log('message', message);
    // console.log('existingMessage', existingMessage);

    const { id, subject, body, receivers, createdAt, updatedAt, sender } =
      message;

    const senderType = sender.username ? 'Student' : 'Account';
    const receiver = receivers[0];

    const baseMessage = {
      createdAt,
      updatedAt,
      sender: {
        type: senderType === 'Student' ? 'CourseStudent' : 'CourseAdvisor',
        id: sender.id,
      },
      chat: chatId,
      subject,
      text: body,
      type: 'text',
      // Set read at for the receiver
      readAt: {
        [receiver.receiverType === 'Student' ? studentId : advisorId]:
          receiver.readAt,
      },
      provider: providerId,
    };

    // Update existing message
    if (existingMessage) {
      Logger.info(`      - Updating existing message`);
      await existingMessage.updateOne(baseMessage, {
        timestamps: false,
      });
    }

    // Create new message
    else {
      Logger.info(`      - Creating new message`);
      existingMessage = await ChatMessage.create({
        ...baseMessage,
        _id: id,
      });

      // Set timestamps (not possible in create...)
      await existingMessage.updateOne(
        { updatedAt, createdAt },
        { timestamps: false }
      );
    }
  } catch {
    //Logger.error(`Error when trying to import message`, error);
  }
}

export default importMessages;
