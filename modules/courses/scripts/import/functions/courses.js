import _ from 'lodash';

import Logger from '#utils/logger.js';
import { isArray } from '#utils/types.js';

import Course from '../../../models/Course.js';

import importLessons from './lessons.js';

import transform from '../utils/transform.js';
import { importImage, getImageUrl } from '../utils/images.js';

async function importCourses(
  sourceEntityId,
  entity,
  providerId,
  axios,
  testCourse,
  options
) {
  const results = {
    imported: 0,
    updated: 0,
  };

  try {
    Logger.info('Importing courses');

    const { data } = await axios.get(
      `/api/entities/${sourceEntityId}/courses`,
      {
        params: {
          filter: {
            fields: ['id'],
            where: {
              isDeleted: false,
              ...(testCourse && { name: testCourse }),
            },
            order: 'name ASC',
          },
        },
      }
    );
    const numberOfRecords = data.length;
    if (numberOfRecords === 0) {
      Logger.info('No courses found to import');
      return results;
    }

    const courseIds = data.map((course) => course.id);
    for (const courseId of courseIds) {
      const course = await loadCourse(courseId, axios);
      const result = await importCourse(
        course,
        entity,
        providerId,
        axios,
        options
      );
      switch (result.status) {
        case 'new':
          results.imported += 1;
          break;
        case 'updated':
          results.updated += 1;
          break;
        default:
          break;
      }
    }

    Logger.info(`${results.imported} courses imported`);
    Logger.info(`${results.updated} courses updated`);
  } catch (error) {
    Logger.error('Error when trying to import courses:', error);
  }

  return results;
}

async function importCourse(course, entity, providerId, axios, options) {
  const result = {
    status: null,
    record: null,
    error: null,
  };

  Logger.info(`- ${course.name}`);

  try {
    let existingCourse = await Course.findOne({
      _id: course.id,
    });

    const {
      id,
      name,
      isEnabled,
      createdAt,
      slug,
      abstract,
      description,
      // trailer,
      image,
      onlineCourse,
      printCourse,
      // requiresRevision,
      // entityId,
      languageId,
      advisorId,
      // categories,
      // lessons,
    } = course;

    const baseCourse = {
      entity,
      provider: providerId,
      enabled: isEnabled,
      deleted: false,
      title: name,
      slug,
      abstract,
      language: languageId ?? 'en',
      onlineCourse,
      correspondenceCourse: printCourse,
      autonomousCourse: true,
      supervisedCourse: true,
      advisor: advisorId,
    };

    // Download image
    const imageUrl = getImageUrl({ image });
    if (imageUrl && !existingCourse?.images?.default) {
      const imageFile = await importImage(imageUrl, entity);
      if (imageFile) baseCourse.images = { default: imageFile };
    }

    // Transform description from slate to tiptap
    const _description = await transform(description, entity);
    baseCourse.body = {
      type: 'doc',
      content: isArray(_description) ? _description : [],
    };

    // Update existing course
    if (existingCourse) {
      await existingCourse.updateOne(baseCourse);

      // Set result
      result.status = 'updated';
      result.record = existingCourse;
    }

    // Create new course
    else {
      existingCourse = await Course.create({
        ...baseCourse,
        _id: id,
        slug,
        createdAt,
      });

      // Set result
      result.status = 'new';
      result.record = existingCourse;
    }

    if (existingCourse && options.lessons) {
      const lessonIds = await importLessons(
        existingCourse,
        entity,
        providerId,
        axios,
        options
      );
      if (!_.isEmpty(lessonIds)) {
        existingCourse.lessons = lessonIds;
        existingCourse.save();
      }
    }
  } catch (error) {
    Logger.error(`Error when trying to import course "${course.name}"`, error);
    result.error = error;
  }

  return result;
}

async function loadCourse(courseId, axios) {
  const { data: course } = await axios.get(`/api/courses/${courseId}`, {
    params: {
      filter: {
        include: ['categories'],
      },
    },
  });

  return course;
}

export default importCourses;
