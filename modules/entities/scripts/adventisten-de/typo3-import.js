import _ from 'lodash';

import Logger from '#utils/logger.js';
import { slugify } from '#utils/strings.js';
import { isValidDate } from '#utils/dates.js';
import { isArray } from '#utils/types.js';

import { saveRemoteImage } from '#modules/images/controllers/imageController.js';
import Person from '#modules/persons/models/Person.js';
import PersonRole from '#modules/persons/models/PersonRole.js';
import SermonEvent from '#modules/sermons/models/SermonEvent.js';

import Entity from '../../models/Entity.js';
import EntityService from '../../models/EntityService.js';
import germanPostalCodes from './data/german-postal-codes.js';
import administrations from './data/sermonplanner-administrations.js';
import locationsSermonPlanner from './data/sermonplanner-locations.js';
import locationsMappings from './data/locations-mappings.js';
import sermonsSermonPlanner from './data/sermonplanner-events.js';

const { CHURCHFINDER_API_URL, CHURCHFINDER_API_SECRET } = process.env;

const importImage = async function (publicUrl, entity) {
  try {
    // Upload the file to the CDN
    const imageFile = await saveRemoteImage(publicUrl, entity);
    // Logger.info('Imported image from', publicUrl);
    return imageFile;
  } catch (error) {
    Logger.error(`Error when trying to obtain image from ${publicUrl}`, error);
  }
};

const arrayToTree = function (
  array,
  parent = 0,
  idField = 'id',
  parentField = 'parent'
) {
  return array
    .filter((item) => item[parentField] === parent)
    .map((child) => ({
      ...child,
      children: arrayToTree(array, child[idField], idField, parentField),
    }));
};

/**
 * Importing pastors from the sermon planner (TYPO3 v6.2, adventisten.de)
 */
export const importPastors = async function (entity, axios) {
  let results = {};

  let importCount = 0;
  let updateCount = 0;

  try {
    Logger.info('Importing pastors');

    const params = {
      type: 2000,
      table: 'tx_amslocations_contact_persons',
      sorting: 'lastname ASC, middlename ASC, firstname ASC',
      hidden: 1,
      related: 1,
      filter: {
        pid: { operator: '=', value: 145 },
        // CONDITION: 'AND',
        // uid: {
        //   operator: '=',
        //   value: 726, // Christian Badorrek
        // },
      },
    };

    const { data: pastorsCount } = await axios.get('/', {
      params: { ...params, count: 1 },
    });

    const limit = 50;
    let offset = 0;

    const { numberOfRecords } = pastorsCount;

    if (numberOfRecords > 0) {
      Logger.info(`Attempting to import ${numberOfRecords} pastors`);

      while (offset < numberOfRecords) {
        Logger.info(
          `Fetching pastors ${offset + 1} to ${Math.min(
            limit + offset,
            numberOfRecords
          )} of ${numberOfRecords}\n`
        );

        const { data: pastors } = await axios.get('/', {
          params: { ...params, limit: `${offset},${limit}` },
        });

        for (const pastor of pastors) {
          try {
            if (_.isEmpty(pastor.lastname)) {
              Logger.warning(
                `Not importing pastor with UID ${pastor.uid} because its name is empty!`
              );
              continue;
            }

            const existingPerson = await Person.findOne({
              importIDs: {
                $elemMatch: {
                  type: 'church-finder',
                  recordID: `${pastor.uid}`,
                },
              },
            });

            const {
              crdate,
              tstamp,
              hidden,
              deleted,
              gender,
              title,
              firstname,
              middlename,
              lastname,
              image,
              // position,
              street,
              zip,
              city,
              // fk_state,
              // fk_country,
              // latitude,
              // longitude,
              phone,
              mobile,
              fax,
              email,
              // show_in_finder,
              // conference,
              // old_uid,
            } = pastor;

            const basePerson = {
              entity,
              enabled: !hidden,
              deleted,
              gender,
              prefix: title,
              firstName: firstname,
              middleName: middlename,
              lastName: lastname,
              address: {
                street,
                zip,
                city,
                state: germanPostalCodes.find((plz) => plz.zipcode === zip)
                  ?.state,
                country: 'DE',
              },
              phone,
              mobile,
              fax,
              email,
            };

            // Avatar
            if (image?.publicUrl) {
              if (!existingPerson || !existingPerson.avatar) {
                const imageFile = await importImage(image.publicUrl, entity);
                if (imageFile) basePerson.avatar = imageFile;
              }
            }

            // Generate person's slug
            const fullName =
              `${basePerson.prefix} ${basePerson.firstName} ${basePerson.middleName} ${basePerson.lastName}`
                .replace(/undefined/g, '')
                .replace(/  +/g, ' ') // Replace multiple spaces with single space
                .trim();

            Logger.info(fullName);

            if (existingPerson) {
              // Update the existing pastor
              await existingPerson.updateOne(basePerson);
              updateCount += 1;
            } else {
              // Ensure person has a valid slug within the entity
              const slug = await Person.getAvailableSlugEntity(
                slugify(fullName),
                entity
              );

              // Add import ID
              basePerson.importIDs = [
                {
                  type: 'church-finder',
                  recordID: `${pastor.uid}`,
                },
              ];

              // Create the new pastor
              await Person.create({
                ...basePerson,
                slug,
                createdAt: crdate,
                updatedAt: tstamp,
              });

              importCount += 1;
            }
          } catch (error) {
            Logger.error(
              `Error when trying to import pastor "${pastor.firstname} ${pastor.lastname}" (uid: ${pastor.uid})`,
              error
            );
          }
        }

        // Increase the offset
        offset += limit;
      }
    }

    if (importCount + updateCount === 0) {
      Logger.info('No pastors found to import');
    } else {
      Logger.info(`${importCount} pastors imported`);
      Logger.info(`${updateCount} pastors updated`);
    }
  } catch (error) {
    Logger.error('Error when trying to import pastors:', error);
  } finally {
    results = {
      imported: importCount,
      updated: updateCount,
    };
  }

  return results;
};

/**
 * Importing church services from the sermon planner (TYPO3 v4.5, predigtplan.adventisten.de)
 */
// TODO Check why entity is not used
// eslint-disable-next-line no-unused-vars
const importChurchServices = async function (entity) {
  let results = {};

  let importCount = 0;
  let updateCount = 0;

  const missingLocations = {};

  try {
    Logger.info('Importing church services');

    // Get church entities
    const churchEntities = await Entity.find(
      {
        type: 'church',
        importIDs: {
          $elemMatch: {
            type: 'sermon-planner',
          },
        },
      },
      { id: 1, importIDs: 1, name: 1 }
    );

    // Get existing church services
    // const existingChurchServices = await SermonEvent.find({
    //   deleted: false,
    // });

    for (const churchService of sermonsSermonPlanner) {
      try {
        if (
          _.isEmpty(churchService.firstname) &&
          _.isEmpty(churchService.lastname)
        ) {
          Logger.warning(
            `Not importing church service for ${churchService.event_date} (church uid: ${churchService.location_uid}) because preacher is empty!`
          );
          continue;
        }

        const { location_uid, event_date, event_title, firstname, lastname } =
          churchService;

        // Ignore church services of missing churches
        if (missingLocations[location_uid]) continue;

        // TODO OGU: Only import Darmstadt-Marienhöhe
        // if (location_uid !== '415') {
        //   continue;
        // }

        const church = churchEntities.find((c) =>
          c.importIDs.find(
            (importID) =>
              importID.type === 'sermon-planner' &&
              importID.recordID === location_uid
          )
        );

        if (!church) {
          Logger.warning(
            `Church missing (uid: ${location_uid}). Not importing church services!`
          );
          const location = locationsSermonPlanner.find(
            (c) => c.locations_uid === location_uid
          );
          missingLocations[location_uid] = location?.locations_title;
          continue;
        }

        const baseChurchService = {
          entity: church,
          date: new Date(event_date),
          // preacher: null,
          // time: null,
          // place: null,
          // title: null,
        };

        // Add preacher
        const preacher = await Person.findOne({
          firstName: firstname,
          lastName: lastname,
        });
        if (preacher) {
          baseChurchService.preacher = preacher;
        } else {
          baseChurchService.firstName = firstname.trim();
          baseChurchService.lastName = lastname.trim();
        }

        // Add special
        const specials = {};
        switch (event_title) {
          case 'Taufgottesdienst':
            specials.baptism = true;
            break;
          case 'Abendmahl':
            specials.communion = true;
            break;
          case 'Waldgottesdienst':
            specials.outdoorService = true;
            break;
          case 'Erntedank':
            specials.pathfinderSabbath = true;
            break;
          case 'Frauensabbat':
            specials.womenSabbath = true;
            break;
          case 'Jugendsabbat':
            specials.youthSabbath = true;
            break;
          default:
            break;
        }
        if (_.isEmpty(specials)) {
          baseChurchService.special = specials;
        }

        const existingChurchService = await SermonEvent.findOne({
          entity: church,
          date: new Date(event_date),
        });

        if (existingChurchService) {
          await existingChurchService.updateOne(baseChurchService);
          updateCount += 1;
        } else {
          await SermonEvent.create(baseChurchService);
          importCount += 1;
        }

        Logger.info(
          existingChurchService ? 'UPDATE' : 'NEW',
          '|',
          church.name,
          '|',
          event_date,
          '|',
          preacher
            ? `${`${preacher.firstName} ${preacher.lastName}`.trim()} (Person)`
            : `${firstname} ${lastname}`
                .replace(/undefined/g, '')
                .replace(/  +/g, ' ') // Replace multiple spaces with single space
                .trim()
        );
      } catch (error) {
        Logger.error(
          `Error when trying to import church service for "${churchService.event_date}, ${churchService.event_title}" (church uid: ${churchService.location_uid})`,
          error
        );
      }
    }
  } catch (error) {
    Logger.error('Error when trying to import sermons:', error);
  } finally {
    if (!_.isEmpty(missingLocations)) {
      Logger.warning(
        'Missing churches (church services not imported)',
        missingLocations
      );
    }

    results = {
      imported: importCount,
      updated: updateCount,
      missingLocations,
    };
  }

  return results;
};

/**
 * Import single administration from the sermon planner (TYPO3 v4.5, predigtplan.adventisten.de)
 */
const importAdministration = async function (
  network,
  administration,
  parent,
  treeLevel,
  results
) {
  try {
    if (_.isEmpty(administration.title)) {
      Logger.warning(
        `Not importing administration with UID ${administration.uid} because its title is empty!`
      );
      return;
    }

    const existingAdministration = await Entity.findOne({
      importIDs: {
        $elemMatch: {
          type: 'sermon-planner',
          recordID: `${administration.uid}`,
        },
      },
    });

    const { title, code, link, hidden } = administration;

    const entityTypes = {
      0: 'unionConference', // Verband
      1: 'conference', // Vereinigung
      2: 'convent', // Konvent
      3: 'district', // Bezirk
    };

    const baseAdministration = {
      network: network,
      enabled: hidden === '0',
      deleted: false,
      name: title,
      code,
      parent,
      siteURL: link?.replace('_blank', '').trim().toLowerCase(),
      language: 'de',
      type: entityTypes[treeLevel],
      location: {
        type: 'Point',
        coordinates: [0, 0],
      },
    };

    Logger.info(baseAdministration.name, '|', baseAdministration.type);

    let importedAdministration;

    // Update the existing administration
    if (existingAdministration) {
      // Don't touch parent of first level administrations (union conferences)
      if (!parent) delete baseAdministration.parent;
      await existingAdministration.updateOne(baseAdministration);
      importedAdministration = existingAdministration;
      results.updated += 1;
    }

    // Create new administration
    else {
      const slug = slugify(title);

      // Ensure administration has a valid slug within its siblings
      // TODO: slug = await Entity.getAvailableSlug(slug);

      // Add import ID
      baseAdministration.importIDs = [
        {
          type: 'sermon-planner',
          recordID: `${administration.uid}`,
        },
      ];

      // Create the new administration
      importedAdministration = await Entity.create({
        ...baseAdministration,
        slug,
        // createdAt: crdate, // TODO: crdate?
        // updatedAt: administration.tstamp, // TODO: tstamp?
      });
      results.imported += 1;
    }

    // Import child administrations
    if (administration.children) {
      for (const childAdministration of administration.children) {
        await importAdministration(
          network,
          childAdministration,
          importedAdministration,
          treeLevel + 1,
          results
        );
      }
    }
  } catch (error) {
    Logger.error(
      `Error when trying to import administration "${administration.title}":`,
      error
    );
  }
};

/**
 * Importing administrations from the sermon planner (TYPO3 v4.5, predigtplan.adventisten.de)
 */
export const importAdministrations = async function (entity) {
  const results = {
    imported: 0,
    updated: 0,
  };

  Logger.info('Importing administrations');

  // Create administrations tree from flat list
  const administrationsTree = arrayToTree(
    administrations,
    '0',
    'uid',
    'fk_administration'
  );

  for (const administration of administrationsTree) {
    await importAdministration(
      entity.network,
      administration,
      null,
      0,
      results
    );
  }

  if (results.imported + results.updated === 0) {
    Logger.info('No administrations found to import');
  } else {
    Logger.info(`${results.imported} administrations imported`);
    Logger.info(`${results.updated} administrations updated`);
  }

  return results;
};

/**
 * Import churches from the church finder (TYPO3 v6.2, adventisten.de)
 * Link the church to the administration of the sermon planner (TYPO3 v.4.5, predigtplan.adventisten.de)
 * Churches between both systems are matched by church title (and locations-mappings.js).
 */
export const importChurches = async function (entity, axios) {
  let results = {};

  let importCount = 0;
  let updateCount = 0;

  const missingAdministrations = {};
  const privateImages = {};

  try {
    Logger.info('Importing churches');

    const params = {
      type: 2000,
      table: 'tx_amslocations_locations',
      sorting: 'title ASC',
      hidden: 1,
      related: 1,
      filter: {
        pid: { operator: '=', value: 146 },
        // CONDITION: 'AND',
        // uid: {
        //   operator: '=',
        //   value: 510, // Aalen (image, 2 services)
        //   value: 894, // Siegen (image, 2 services)
        //   value: 1083, // Darmstadt-Marienhöhe (3 services)
        //   value: 1196, // Neermoor (ehemals Leer)
        // },
      },
    };

    const { data: locationCount } = await axios.get('/', {
      params: { ...params, count: 1 },
    });

    const limit = 50;
    let offset = 0;

    const { numberOfRecords } = locationCount;

    if (numberOfRecords > 0) {
      Logger.info(`Attempting to import ${numberOfRecords} churches`);

      const _administrations = await Entity.aggregate()
        .match({
          importIDs: { $elemMatch: { type: 'sermon-planner' } },
        })
        .addFields({
          importID: {
            $filter: {
              input: '$importIDs',
              as: 'importID',
              cond: { type: 'sermon-planner' },
            },
          },
        })
        .project({
          _id: 1,
          name: 1,
          type: 1,
          importIDs: 1,
          // TODO: $getField only since mongodb 5
          // importType: 'sermon-planner',
          // importID: {
          //   $getField: {
          //     field: 'recordID',
          //     input: { $arrayElemAt: ['$importID', 0] },
          //   },
          // },
        });

      while (offset < numberOfRecords) {
        Logger.info(
          `Fetching churches ${offset + 1} to ${Math.min(
            limit + offset,
            numberOfRecords
          )} of ${numberOfRecords}\n`
        );

        const { data: churches } = await axios.get('/', {
          params: { ...params, limit: `${offset},${limit}` },
        });

        for (const church of churches) {
          try {
            if (_.isEmpty(church.title)) {
              Logger.warning(
                `Not importing church with UID ${church.uid} because its title is empty!`
              );
              continue;
            }

            const existingChurch = await Entity.findOne({
              importIDs: {
                $elemMatch: {
                  type: 'church-finder',
                  recordID: `${church.uid}`,
                },
              },
            });

            const {
              crdate,
              tstamp,
              hidden,
              deleted,
              title,
              image,
              street,
              additional_address,
              zip,
              city,
              // fk_state, // using germonPostaCodes.js instead
              // fk_country, // using germonPostaCodes.js instead
              latitude,
              longitude,
              fk_services,
              website,
              is_accessible,
            } = church;

            const state = germanPostalCodes.find(
              (plz) => plz.zipcode === zip
            )?.state;
            const country = state ? 'DE' : null;

            const baseChurch = {
              network: entity.network,
              enabled: !hidden,
              deleted,
              name: title,
              language: 'de',
              type: 'church',
              address: {
                street,
                additionalAddress: additional_address,
                zip,
                city,
                state,
                country,
              },
              amenities: {
                accessibility: {
                  stepFreeEntrance: is_accessible,
                  accessibleParkingSpots: false,
                },
              },
              location: {
                type: 'Point',
                coordinates:
                  longitude && latitude ? [longitude, latitude] : [0, 0],
              },
              ...(website && {
                siteURL: `https://${website
                  .replace('http://', '')
                  .replace('https://', '')
                  .replace(/\/$/, '')}`, // removing trailing slash
              }),
            };

            // Add parent entity (administration)
            // 1. Check if title is different in sermonplanner and churchfinder
            const mappedTitle = locationsMappings.find(
              (location) => location.churchfinder_title === title
            )?.sermonplanner_title;
            const _title = mappedTitle ?? title;

            // 2. Get matching church from sermon planner (by comparing title)
            const sermonPlannerChurch = locationsSermonPlanner.find(
              (location) => location.locations_title.trim() === _title.trim()
            );

            // 3. Get assigned administration
            if (sermonPlannerChurch) {
              baseChurch.parent = _administrations.find((a) =>
                a.importIDs.find(
                  (importID) =>
                    importID.type === 'sermon-planner' &&
                    importID.recordID ===
                      sermonPlannerChurch.administrations_uid
                )
              )?._id;
            }

            if (!baseChurch.parent) {
              missingAdministrations[church.uid] = title;
              Logger.warning(
                `No administration found for the church '${title}'`
              );
            }

            // Image
            if (image?.publicUrl) {
              const isPublicImage = !image.publicUrl.includes(
                'index.php?eID=dumpFile'
              );
              if (isPublicImage) {
                if (!existingChurch || !existingChurch.image) {
                  const imageFile = await importImage(image.publicUrl, entity);
                  if (imageFile) baseChurch.image = imageFile;
                }
              } else {
                privateImages[church.uid] = title;
                Logger.warning(
                  `Non-public file: Could not download image of "${title}" (uid: ${church.uid})`
                );
              }
            }

            // Function to generate services (it's called at the end, once the church is created)
            const generateServices = async (_church) => {
              const services = [];

              const sortedServices = fk_services.sort((a) => {
                if (a.fk_service_type?.title === 'Gottesdienst') return -1;
                return 0;
              });

              for (const service of sortedServices) {
                if (fk_services.deleted) continue;

                const {
                  fk_service_type,
                  weekday,
                  time_begin,
                  fk_contact_persons,
                } = service;

                const serviceTypes = {
                  Gottesdienst: 'divineService',
                  Jugendstunde: 'youthGroup',
                  Pfadfinderprogramm: 'pathfinderGroup',
                };

                const serviceTitle = fk_service_type?.title;

                if (!serviceTitle) {
                  Logger.warning(`no title, do nothing`);
                  continue;
                }

                const weekdays = {
                  0: 'su',
                  1: 'mo',
                  2: 'tu',
                  3: 'we',
                  4: 'th',
                  5: 'fr',
                  6: 'sa',
                };

                const baseService = {
                  entity: _church,
                  enabled: !service.hidden,
                  type: serviceTypes[title],
                  title: serviceTitle,
                  weekday: weekdays[weekday],
                  startsAt: isValidDate(time_begin)
                    ? new Date(time_begin).toLocaleTimeString('de-DE', {
                        timeStyle: 'short',
                        timeZone: 'UTC',
                      })
                    : null,
                  ...(service.website && {
                    website: `https://${service.website
                      .replace('http://', '')
                      .replace('https://', '')
                      .replace(/\/$/, '')}`, // remove trailing slash
                  }),
                };

                // Function to generate contact persons (it's called at the end, once the service is created)
                // TODO: Check why _service is not used
                const generateContactPersons = async (_service) => {
                  const contactPersons = [];

                  if (
                    fk_contact_persons &&
                    !isArray(fk_contact_persons) &&
                    isArray(fk_contact_persons.tx_amslocations_contact_persons)
                  ) {
                    // Loop through contact persons
                    for (const contactPerson of fk_contact_persons.tx_amslocations_contact_persons) {
                      const existingPerson = await Person.findOne(
                        {
                          importIDs: {
                            $elemMatch: {
                              type: 'church-finder',
                              recordID: contactPerson.uid.toString(),
                            },
                          },
                        },
                        { _id: 1, firstName: 1, lastName: 1 }
                      );
                      if (existingPerson) {
                        Logger.info(
                          _church.name,
                          '|',
                          baseService.title,
                          '|',
                          existingPerson.firstName,
                          existingPerson.lastName,
                          '|',
                          contactPerson.position || 'Pastor'
                        );

                        const basePersonRole = {
                          entity: _church,
                          person: existingPerson,

                          ...(contactPerson.position === 'Pastor'
                            ? {
                                role: 'pastor',
                              }
                            : contactPerson.position
                              ? {
                                  role: 'custom',
                                  title: contactPerson.position,
                                }
                              : contactPerson.show_in_finder
                                ? {
                                    role: 'pastor',
                                  }
                                : {
                                    role: 'custom',
                                    title: 'Ansprechpartner',
                                  }),
                        };

                        const existingPersonRole = await PersonRole.findOne({
                          entity: _church,
                          person: existingPerson,
                          role: basePersonRole.role,
                          title: basePersonRole.title,
                        });

                        if (existingPersonRole) {
                          // Update the existing person ref
                          await existingPersonRole.updateOne(basePersonRole);
                          contactPersons.push(existingPersonRole._id);
                        } else {
                          // Create the new person ref
                          const newPersonRole =
                            await PersonRole.create(basePersonRole);
                          contactPersons.push(newPersonRole._id);
                        }
                      }
                    }
                  }

                  return contactPersons;
                };

                const existingService = await EntityService.findOne({
                  entity: _church,
                  importIDs: {
                    $elemMatch: {
                      type: 'church-finder',
                      recordID: `${service.uid}`,
                    },
                  },
                });

                if (existingService) {
                  // Add the services
                  baseService.contactPersons =
                    await generateContactPersons(existingService);

                  await existingService.updateOne(baseService);
                  services.push(existingService._id);
                } else {
                  // Generate service's slug
                  let slug = slugify(baseService.title);

                  // Ensure service has a valid slug within the church
                  slug = await EntityService.getAvailableSlug(slug, _church.id);

                  // Add import ID
                  baseService.importIDs = [
                    {
                      type: 'church-finder',
                      recordID: `${service.uid}`,
                    },
                  ];

                  // Create the new service
                  const newService = await EntityService.create({
                    ...baseService,
                    slug,
                    createdAt: service.crdate,
                    updatedAt: service.tstamp,
                  });

                  // Add the contact persons
                  newService.contactPersons =
                    await generateContactPersons(newService);
                  await newService.save();

                  services.push(newService._id);
                }
              }

              return services;
            };

            // Add import ID
            baseChurch.importIDs = [
              {
                type: 'church-finder',
                recordID: `${church.uid}`,
              },
              ...(sermonPlannerChurch
                ? [
                    {
                      type: 'sermon-planner',
                      recordID: `${sermonPlannerChurch.locations_uid}`,
                    },
                  ]
                : []),
            ];

            if (existingChurch) {
              // Add the services
              baseChurch.services = await generateServices(existingChurch);

              // Update the existing church
              await existingChurch.updateOne(baseChurch);

              updateCount += 1;
            } else {
              const slug = slugify(church.title);

              // Ensure church has a valid slug within its siblings
              // TODO: slug = await Entity.getAvailableSlug(slug);

              // Create the new church
              const newChurch = await Entity.create({
                ...baseChurch,
                slug,
                createdAt: crdate,
                updatedAt: tstamp,
              });

              // Add the services
              newChurch.services = await generateServices(newChurch);
              await newChurch.save();

              importCount += 1;
            }
          } catch (error) {
            Logger.error(
              `Error when trying to import church "${church.title}" (uid: ${church.uid})`,
              error
            );
          }
        }

        // Increase the offset
        offset += limit;
      }
    }

    if (importCount + updateCount === 0) {
      Logger.info('No churches found to import');
    } else {
      Logger.info(`${importCount} churches imported`);
      Logger.info(`${updateCount} churches updated`);
    }
  } catch (error) {
    Logger.error('Error when trying to import churches:', error);
  } finally {
    if (!_.isEmpty(missingAdministrations)) {
      Logger.warning(
        'Churches without administrations',
        missingAdministrations
      );
    }
    if (!_.isEmpty(privateImages)) {
      Logger.warning('Churches without images', privateImages);
    }

    results = {
      imported: importCount,
      updated: updateCount,
      missingAdministrations,
      privateImages,
    };
  }

  return results;
};

/**
 * Import contact persons from the church finder (TYPO3 v6.2, adventisten.de)
 */
export const importContactPersons = async function (
  entity,
  pid,
  importType,
  axios
) {
  let results = {};

  let importCount = 0;
  let updateCount = 0;

  try {
    Logger.info('Importing contact persons');

    const params = {
      type: 2000,
      table: 'tx_amscontacts_persons',
      sorting: 'lastname ASC, firstname ASC',
      hidden: 1,
      related: 1,
      filter: {
        pid: { operator: '=', value: pid },
        CONDITION: 'AND',
        uid: {
          operator: '=',
          value: 540, // Thomas Alberts
        },
      },
    };

    const { data: contactPersonsCount } = await axios.get('/', {
      params: { ...params, count: 1 },
    });

    const limit = 50;
    let offset = 0;

    const { numberOfRecords } = contactPersonsCount;

    if (numberOfRecords > 0) {
      Logger.info(`Attempting to import ${numberOfRecords} contact persons`);

      while (offset < numberOfRecords) {
        Logger.info(
          `Fetching contact persons ${offset + 1} to ${Math.min(
            limit + offset,
            numberOfRecords
          )} of ${numberOfRecords}\n`
        );

        const { data: contactPersons } = await axios.get('/', {
          params: { ...params, limit: `${offset},${limit}` },
        });

        for (const contactPerson of contactPersons) {
          try {
            if (_.isEmpty(contactPerson.lastname)) {
              Logger.warning(
                `Not importing contact person with UID ${contactPerson.uid} because its name is empty!`
              );
              continue;
            }

            const existingPerson = await Person.findOne({
              importIDs: {
                $elemMatch: {
                  type: importType,
                  recordID: `${contactPerson.uid}`,
                },
              },
            });

            const {
              crdate,
              tstamp,
              hidden,
              deleted,
              // fk_group, // NDV, SDV, ...
              gender,
              prefix,
              firstname,
              middlename,
              lastname,
              suffix,
              photo,
              // position, // Hausverwaltung NRW, ...
              // organization,
              // department,
              // birthday, // always null
              // website, // TODO: add to Person model
              fk_addresses,
              fk_phonenumbers,
              // fk_email_addresses,
            } = contactPerson;

            const basePerson = {
              entity,
              enabled: !hidden,
              deleted,
              gender,
              prefix,
              firstName: firstname,
              middleName: middlename,
              lastName: lastname,
              suffix,
              // phone,
              // mobile,
              // fax,
              // email,
            };

            // Address (first address only)
            const addresses = fk_addresses
              .filter((address) => !address.deleted && !address.hidden)
              .sort((a, b) => (a.prefered && !b.prefered ? -1 : 0));
            if (addresses.length > 0) {
              const {
                // addresstype, // work, home, postal
                // prefered, // just used for sorting prefered addresses first
                street,
                extendedaddress,
                zip,
                city,
                // fk_state, // using germonPostaCodes.js instead
                // fk_country, // using germonPostaCodes.js instead
                // latitude, // TODO: add to Person model
                // longitude, // TODO: add to Person model
                // map, // always 0
              } = addresses[0];

              const state = germanPostalCodes.find(
                (plz) => plz.zipcode === zip
              )?.state;

              const country = state ? 'DE' : null;

              basePerson.address = {
                street,
                additionalAddress: extendedaddress,
                zip,
                city,
                state,
                country,
              };
            }
            if (addresses.length > 1) {
              Logger.warning(
                `Not importing additional addresses of contact person with UID ${contactPerson.uid} (${addresses.length} addresses)!`
              );
            }

            // Add fax
            basePerson.fax = fk_phonenumbers.find(
              (number) =>
                !number.deleted && !number.hidden && number.numbertype === 'fax'
            )?.phonenumber;

            // Add phone
            basePerson.phone = fk_phonenumbers.find(
              (number) =>
                !number.deleted &&
                !number.hidden &&
                number.numbertype === 'work'
            )?.phonenumber;

            // Add mobile
            basePerson.mobile = fk_phonenumbers.find(
              (number) =>
                !number.deleted &&
                !number.hidden &&
                number.numbertype === 'cell'
            )?.phonenumber;

            // Avatar
            if (photo?.publicUrl) {
              if (!existingPerson || !existingPerson.avatar) {
                const imageFile = await importImage(photo.publicUrl, entity);
                if (imageFile) basePerson.avatar = imageFile;
              }
            }

            if (existingPerson) {
              // Update the existing contact person
              await existingPerson.updateOne(basePerson);

              updateCount += 1;
            } else {
              // Generate person's slug
              const fullName = `${basePerson.prefix} ${basePerson.firstName} ${basePerson.middleName} ${basePerson.lastName}`;

              // Ensure person has a valid slug within the entity
              const slug = await Person.getAvailableSlugEntity(
                slugify(fullName),
                entity
              );

              // Add role
              // TODO: add role and position to PersonRole (roles is deprecated)
              // basePerson.roles = ['pastor'];

              // Add import ID
              basePerson.importIDs = [
                {
                  type: importType,
                  recordID: `${contactPerson.uid}`,
                },
              ];

              // Create the new contact person
              await Person.create({
                ...basePerson,
                slug,
                createdAt: crdate,
                updatedAt: tstamp,
              });

              importCount += 1;
            }
          } catch (error) {
            Logger.error(
              `Error when trying to import contact person "${contactPerson.firstname} ${contactPerson.lastname}" (uid: ${contactPerson.uid})`,
              error
            );
          }
        }

        // Increase the offset
        offset += limit;
      }
    }

    if (importCount + updateCount === 0) {
      Logger.info('No contact persons found to import');
    } else {
      Logger.info(`${importCount} contact persons imported`);
      Logger.info(`${updateCount} contact persons updated`);
    }
  } catch (error) {
    Logger.error('Error when trying to import contact persons:', error);
  } finally {
    results = {
      imported: importCount,
      updated: updateCount,
    };
  }

  return results;
};

const importData = async function (entity) {
  Logger.info('Starting church import');

  if (!CHURCHFINDER_API_URL || !CHURCHFINDER_API_SECRET) {
    Logger.error('Churchfinder API configuration missing');
    return { error: 'Churchfinder API configuration missing' };
  }

  // const pastors = await importPastors(entity, _axios);
  // const administrations = export await importAdministrations(entity);
  // const churches = await importChurches(entity, _axios);
  const churchServices = await importChurchServices(entity);
  // const contactPersons = await importContactPersons(
  //   entity,
  //   32,
  //   'contact-persons',
  //   _axios
  // );
  // const departments = await importContactPersons(
  //   entity,
  //   142,
  //   'departments',
  //   _axios
  // );

  Logger.info('Done importing data');

  return {
    // pastors,
    // administrations,
    // churches,
    churchServices,
    // contactPersons,
    // departments,
  };
};

export default {
  importData,
};
