import renderEmail from './templates/utils/renderEmail.js';

// Test data for email rendering
const testData = {
  language: 'en',
  template: 'newAccount',
  colors: {
    primary: {
      100: '#e6f3ff',
      200: '#bfddff',
      300: '#99c7ff',
      400: '#4da6ff',
      500: '#0080ff',
      600: '#0066cc',
      700: '#004d99',
      800: '#003366',
      900: '#001a33',
    },
  },
  values: {
    title: 'Welcome to',
    entityName: 'Test Organization',
    subtitle: 'Your account has been created successfully.',
    linkURL: 'https://example.com/activate',
    linkText: 'Activate Account',
    linkNotWorking:
      "If the button above doesn't work, copy and paste this link:",
    duration: '24 hours',
    subtext: 'This link will expire in',
    subtext2: 'If you have any questions, please contact our support team.',
    logoURL: 'https://via.placeholder.com/200x60/0080ff/ffffff?text=LOGO',
    entityInfo: {
      emailFooter: [
        'Test Organization',
        '123 Main Street',
        'City, State 12345',
      ],
      phone: '+****************',
      email: '<EMAIL>',
    },
  },
};

console.log('Testing email rendering with Maizzle 5...');

renderEmail(testData)
  .then((html) => {
    console.log('✅ Email rendering successful!');
    console.log('HTML length:', html.length);

    // Check if primary colors are being used (converted to inline styles)
    if (html.includes('#0080ff')) {
      console.log('✅ Primary color styles found in output');
    } else {
      console.log('⚠️  Primary color styles not found in output');
    }

    // Check if Tailwind classes are being processed
    if (html.includes('background-color') || html.includes('color:')) {
      console.log('✅ CSS styles found in output (classes processed)');
    } else {
      console.log('⚠️  No CSS styles found - classes may not be processed');
    }

    // Check if template inheritance worked
    if (html.includes('<extends') || html.includes('<block')) {
      console.log(
        '⚠️  Template inheritance not processed - still contains extends/block tags'
      );
    } else {
      console.log('✅ Template inheritance processed correctly');
    }

    // Save output for inspection
    import('fs').then((fs) => {
      const filename = `test-output-${Date.now()}.html`;
      fs.writeFileSync(filename, html);
      console.log(`📄 Output saved to ${filename}`);
      console.log('First 200 chars of output:', html.substring(0, 200));
    });
  })
  .catch((error) => {
    console.error('❌ Email rendering failed:');
    console.error(error.message);
    console.error(error.stack);
  });
