import axios from 'axios';

import Logger from '#utils/logger.js';
import { notifyAdmins } from '#utils/notifier.js';
import { getBaseURL, getHeaders } from './utils.js';
import { removeDomainFromDNS } from './removeDomainDNS.js';

const { REGISTER_DOMAINS, DNS_REGISTER, DNS_PROVIDER_TOKEN } = process.env;

export const removeDomain = async ({
  domain = '',
  design = '',
  region = '',
  zoneIdentifier = '',
  provider = {},
} = {}) => {
  if (REGISTER_DOMAINS && domain) {
    const { defaultRegion, params, supportsRedirects } = provider;
    region = region || defaultRegion;

    Logger.info('Attempting to remove domain:', domain, 'from region:', region);

    const baseUrl = getBaseURL(provider.baseUrl, { design, region });

    if (domain.includes('localhost')) {
      Logger.info('Not removing domain because is a local dev domain');
      return false;
    }

    // Removing subdomain from DNS provider if it was added by the API
    const subdomain = domain.split('.')[0];

    // Only remove in DNS provider if zoneIdentifier is provided and DNS_REGISTER is true
    if (DNS_REGISTER && DNS_PROVIDER_TOKEN && zoneIdentifier && subdomain) {
      await removeDomainFromDNS({
        domain,
        region,
        zoneIdentifier,
        subdomain,
      });
    }

    // Remove domain from hosting provider
    const headers = await getHeaders(provider);

    try {
      const { data } = await axios.get(`${baseUrl}${params || ''}`, {
        headers,
      });

      // Attempt to remove redirect only if it exists
      const redirects = [];
      if (supportsRedirects) {
        redirects.push(...data.domains.filter((d) => d.redirect === domain));

        for (let index = 0; index < redirects.length; index += 1) {
          const redirect = redirects[index];

          await axios.delete(`${baseUrl}/${redirect.name}${params || ''}`, {
            headers,
          });
        }
      }

      // Attempt to remove domain only if it exists
      if (data.domains.find((d) => d.name === domain)) {
        const { getDeletePath } = provider;
        await axios.delete(
          `${baseUrl}/${getDeletePath(domain, data)}${provider.params || ''}`,
          {
            headers,
          }
        );

        notifyAdmins({
          subject: 'Successfully removed domain',
          templateName: 'domainRegistration',
          templateValues: {
            domain,
            region,
            redirects:
              redirects.length > 0 && provider.supportsRedirects
                ? redirects.map((r) => r.name)
                : null,
            isError: false,
          },
        });

        Logger.success('Domain removed:', domain, 'from region:', region);

        return true;
      }

      Logger.info("Domain didn't exist:", domain, 'in region:', region);

      return false;
    } catch (error) {
      notifyAdmins({
        subject: 'Error removing domain',
        templateName: 'domainRegistration',
        templateValues: {
          domain,
          region,
          isError: true,
          error: JSON.stringify(error.response?.data ?? error),
        },
      });

      Logger.error(
        `Couldn't remove domain ${domain} from region ${region} in ${provider.name}:`,
        error.response ?? error
      );

      return false;
    }
  }

  Logger.warning(
    "Domain removal not possible. Make sure it's enabled in env vars and the provided domain is not empty:",
    { REGISTER_DOMAINS, domain, region }
  );

  return false;
};
