<!DOCTYPE {{{ page.doctype || 'html' }}}>
<html
  lang="{{ language || 'en' }}"
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
>
  <head>
    <meta charset="{{ page.charset || 'utf-8' }}" />
    <meta name="x-apple-disable-message-reformatting" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="format-detection"
      content="telephone=no, date=no, address=no, email=no"
    />
    <outlook>
      <xml>
        <o:OfficeDocumentSettings>
          <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
      </xml>
      <style>
        td,
        th,
        div,
        p,
        a,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          font-family: 'Segoe UI', sans-serif;
          mso-line-height-rule: exactly;
        }

        body {
          @apply m-0 p-0 w-full;
          word-break: break-word;
          -webkit-font-smoothing: antialiased;
        }

        img {
          border: 0;
          @apply max-w-full leading-full align-middle;
        }

        @tailwind components;
        @tailwind utilities;

        .mso-leading-exactly {
          mso-line-height-rule: exactly;
        }
      </style>
    </outlook>
    <if condition="subject">
      <title>{{{ subject }}}</title>
    </if>
    <block name="head"></block>
  </head>
  <body class="bg-gray-200">
    <if condition="page.preheader">
      <div class="hidden">{{ page.preheader }}</div>
    </if>
    <div
      role="article"
      aria-roledescription="email"
      aria-label="{{{ subject || '' }}}"
      lang="{{ language || 'en'}}"
    >
      <table class="w-full font-sans">
        <tr>
          <td align="center" class="bg-gray-200">
            <table class="w-200 sm:w-full">
              <tr>
                <td class="py-10 sm:py-8 text-center">
                  <img src="{{logoURL}}" class="w-52 sm:w-44" alt="Logo" />
                </td>
              </tr>
              <tr>
                <td align="center" class="sm:px-6">
                  <table class="w-full">
                    <tr>
                      <td
                        class="bg-white text-gray-800 text-base text-left p-12 sm:px-6 leading-6 rounded-lg"
                      >
                        <block name="template"></block>
                      </td>
                    </tr>
                  </table>
                  <table class="w-full">
                    <tr>
                      <td class="h-10"></td>
                    </tr>
                    <tr>
                      <td class="text-center text-gray-600 text-xs px-6">
                        <each loop="item, index in entityInfo.emailFooter">
                          <p class="mb-1">{{item}}</p>
                        </each>
                        <p class="cursor-default">
                          <if condition="entityInfo.phone">
                            <a
                              href="tel:{{entityInfo.phone}}"
                              class="text-primary-500 no-underline hover:underline"
                            >
                              {{entityInfo.phone}}
                            </a>
                          </if>
                          <if condition="entityInfo.phone && entityInfo.email">
                            &bull;
                          </if>
                          <if condition="entityInfo.email">
                            <a
                              href="mailto:{{entityInfo.email}}"
                              class="text-primary-500 no-underline hover:underline"
                            >
                              {{entityInfo.email}}
                            </a>
                          </if>
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>
