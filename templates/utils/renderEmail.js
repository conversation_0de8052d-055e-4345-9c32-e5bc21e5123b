import { render } from '@maizzle/framework';
import fs from 'fs';
import path from 'path';

import maizzleConf from '../config.js';
import tailwindConf from '../tailwind.config.cjs';
import { setColorsVariables } from './colors.js';
import { getTemplate } from './files.js';

/**
 * Process template inheritance by combining layout and template
 * @param {string} templateContent - The template content with <extends> and <block> tags
 * @returns {string} - The combined HTML with template inheritance resolved
 */
function processTemplateInheritance(templateContent) {
  // Extract the layout path from <extends src="...">
  const extendsMatch = templateContent.match(/<extends\s+src="([^"]+)"/);
  if (!extendsMatch) {
    return templateContent; // No inheritance, return as-is
  }

  const layoutPath = extendsMatch[1];
  // The layout path in the template is relative to the project root, not the templates folder
  const layoutFullPath = path.resolve(layoutPath);

  // Read the layout file
  let layoutContent;
  try {
    layoutContent = fs.readFileSync(layoutFullPath, 'utf8');
  } catch (_error) {
    // If layout file can't be read, return original template content
    return templateContent;
  }

  // Extract blocks from the template
  const blockRegex = /<block\s+name="([^"]+)">([\s\S]*?)<\/block>/g;
  const blocks = {};
  let blockMatch;

  while ((blockMatch = blockRegex.exec(templateContent)) !== null) {
    blocks[blockMatch[1]] = blockMatch[2];
  }

  // Replace blocks in the layout
  let result = layoutContent;
  for (const [blockName, blockContent] of Object.entries(blocks)) {
    const blockPlaceholder = `<block name="${blockName}"></block>`;
    result = result.replace(blockPlaceholder, blockContent);
  }

  // Remove any remaining empty blocks
  result = result.replace(/<block name="[^"]*"><\/block>/g, '');

  return result;
}

/**
 * Renders the indicated Maizzle template with the indicated values
 *
 * @param {Object} options {language: string, template: string, values: object}
 * @return rendered html
 */
export default function renderEmail({
  language = 'de',
  template = '',
  colors = {},
  values = {},
}) {
  return new Promise((resolve, reject) => {
    const templateString = getTemplate(template);

    if (!templateString) {
      reject(new Error('Failed to import required template!'));
    }

    // Process template inheritance (extends/blocks)
    const processedTemplate = processTemplateInheritance(templateString);

    let tailwind = { ...tailwindConf };
    const { primary, secondary, neutral } = colors || {};

    // Set colors variables for tailwind with the provided colors, or fallback to the default ones
    tailwind = setColorsVariables(tailwind, 'primary', primary);
    tailwind = setColorsVariables(tailwind, 'secondary', secondary);
    tailwind = setColorsVariables(tailwind, 'gray', neutral);

    render(processedTemplate, {
      ...maizzleConf,
      css: {
        ...maizzleConf.css,
        tailwind: {
          ...tailwind,
          content: [
            {
              raw: processedTemplate,
              extension: 'html',
            },
          ],
        },
      },
      locals: {
        language,
        ...values,
      },
    })
      .then((render) => resolve(render.html))
      .catch((error) => reject(error));
  });
}
