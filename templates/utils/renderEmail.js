import { render } from '@maizzle/framework';

import maizzleConf from '../config.js';
import tailwindConf from '../tailwind.config.cjs';
import { setColorsVariables } from './colors.js';
import { getStyles, getTemplate } from './files.js';

/**
 * Renders the indicated Maizzle template with the indicated values
 *
 * @param {Object} options {language: string, template: string, values: object}
 * @return rendered html
 */
export default function renderEmail({
  language = 'de',
  template = '',
  colors = {},
  values = {},
}) {
  return new Promise((resolve, reject) => {
    const templateString = getTemplate(template);

    if (!templateString) {
      reject(new Error('Failed to import required template!'));
    }

    let tailwind = { ...tailwindConf };
    const { primary, secondary, neutral } = colors || {};

    // Set colors variables for tailwind with the provided colors, or fallback to the default ones
    tailwind = setColorsVariables(tailwind, 'primary', primary);
    tailwind = setColorsVariables(tailwind, 'secondary', secondary);
    tailwind = setColorsVariables(tailwind, 'gray', neutral);

    render(templateString, {
      tailwind: {
        config: tailwind,
        css: getStyles('reset'),
      },
      maizzle: {
        ...maizzleConf,
        locals: {
          language,
          ...values,
        },
      },
    })
      .then((render) => resolve(render.html))
      .catch((error) => reject(error));
  });
}
