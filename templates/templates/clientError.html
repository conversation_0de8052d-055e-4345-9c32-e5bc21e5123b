<extends src="templates/layout.html">
  <block name="template">
    <p class="m-0 font-medium text-xl">
      Error on client: {{clientName}} (<a
        href="mailto:{{userEmail}}?subject=Backend Error"
        >{{userName}}</a
      >)
    </p>

    <div class="leading-6">&nbsp;</div>
    <p class="m-0 font-medium text-xl">
      <code>{{ error }}</code>
    </p>

    <div class="leading-6">&nbsp;</div>
    <p class="m-0 mb-2 font-medium text-xl">More info:</p>
    <p class="m-0 mb-2">
      <span class="font-semibold">URL</span>: <code>{{ url }}</code>
    </p>
    <p class="m-0 mb-2">
      <span class="font-semibold">User-Agent</span>: {{useragent.browser.name}}
      {{useragent.browser.major}} ({{useragent.browser.version}}) on
      {{useragent.os.name}} {{useragent.os.version}}
      ({{useragent.cpu.architecture}})
    </p>
    <div class="m-0 mb-2">
      <span class="font-semibold">JS Stacktrace</span>:
      <ul class="list-none pl-2">
        <each loop="line in stack">
          <li>
            <code class="font-bold">{{ line[0] }}</code>
            <if condition="line[1] !== undefined">
              in <code>{{ line[1] }}</code>
            </if>
          </li>
        </each>
      </ul>
    </div>
    <each loop="value, key in errorInfo">
      <p class="m-0 mb-2">
        <span class="font-semibold">{{ key }}</span>:
        <code class="whitespace-pre-wrap">{{ value }}</code>
      </p>
    </each>
  </block>
</extends>
